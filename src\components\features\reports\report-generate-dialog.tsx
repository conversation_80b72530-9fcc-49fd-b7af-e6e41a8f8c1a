"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { GenerateReportInput, ReportType } from "@/lib/validations/report"
import { AuditWithRelations } from "@/lib/validations/audit"
import { Wand2, FileText, AlertTriangle, CheckSquare, BarChart3, Users } from "lucide-react"

interface ReportGenerateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  audit: AuditWithRelations | null
  onGenerate: (data: GenerateReportInput) => Promise<void>
  loading?: boolean
}

export function ReportGenerateDialog({
  open,
  onOpenChange,
  audit,
  onGenerate,
  loading = false
}: ReportGenerateDialogProps) {
  const [type, setType] = useState<ReportType>("AUDIT_SUMMARY")
  const [title, setTitle] = useState("")
  const [includeObservations, setIncludeObservations] = useState(true)
  const [includeActions, setIncludeActions] = useState(true)
  const [includeStatistics, setIncludeStatistics] = useState(true)
  const [autoPublish, setAutoPublish] = useState(false)

  const handleGenerate = async () => {
    if (!audit) return

    const generateData: GenerateReportInput = {
      auditId: audit.id,
      type,
      title: title || undefined,
      includeObservations,
      includeActions,
      includeStatistics,
      autoPublish
    }

    await onGenerate(generateData)
    onOpenChange(false)
    
    // Reset form
    setTitle("")
    setType("AUDIT_SUMMARY")
    setIncludeObservations(true)
    setIncludeActions(true)
    setIncludeStatistics(true)
    setAutoPublish(false)
  }

  const reportTypes = [
    {
      value: "AUDIT_SUMMARY" as ReportType,
      label: "Résumé d'audit",
      description: "Vue d'ensemble concise des résultats d'audit",
      icon: FileText
    },
    {
      value: "DETAILED_FINDINGS" as ReportType,
      label: "Constats détaillés",
      description: "Analyse approfondie de toutes les observations",
      icon: AlertTriangle
    },
    {
      value: "ACTION_PLAN" as ReportType,
      label: "Plan d'action",
      description: "Liste des actions correctives à mettre en œuvre",
      icon: CheckSquare
    },
    {
      value: "COMPLIANCE_REPORT" as ReportType,
      label: "Rapport de conformité",
      description: "Évaluation de la conformité aux exigences",
      icon: BarChart3
    },
    {
      value: "EXECUTIVE_SUMMARY" as ReportType,
      label: "Résumé exécutif",
      description: "Synthèse pour la direction et les parties prenantes",
      icon: Users
    }
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5" />
            Générer un rapport automatiquement
          </DialogTitle>
          <DialogDescription>
            {audit && (
              <>Générez automatiquement un rapport pour l'audit "{audit.title}".</>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Type de rapport */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Type de rapport</Label>
            <Select value={type} onValueChange={(value) => setType(value as ReportType)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {reportTypes.map((reportType) => (
                  <SelectItem key={reportType.value} value={reportType.value}>
                    <div className="flex items-center gap-2">
                      <reportType.icon className="h-4 w-4" />
                      <div>
                        <div className="font-medium">{reportType.label}</div>
                        <div className="text-xs text-gray-500">{reportType.description}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Titre personnalisé */}
          <div className="space-y-2">
            <Label htmlFor="title" className="text-sm font-medium">
              Titre du rapport (optionnel)
            </Label>
            <Input
              id="title"
              placeholder="Laissez vide pour un titre automatique"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
          </div>

          {/* Contenu à inclure */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Contenu à inclure</Label>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="observations"
                  checked={includeObservations}
                  onCheckedChange={(checked) => setIncludeObservations(checked as boolean)}
                />
                <Label htmlFor="observations" className="text-sm">
                  Observations et constats
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="actions"
                  checked={includeActions}
                  onCheckedChange={(checked) => setIncludeActions(checked as boolean)}
                />
                <Label htmlFor="actions" className="text-sm">
                  Actions correctives
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="statistics"
                  checked={includeStatistics}
                  onCheckedChange={(checked) => setIncludeStatistics(checked as boolean)}
                />
                <Label htmlFor="statistics" className="text-sm">
                  Statistiques et métriques
                </Label>
              </div>
            </div>
          </div>

          {/* Options de publication */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Options de publication</Label>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="autoPublish"
                checked={autoPublish}
                onCheckedChange={(checked) => setAutoPublish(checked as boolean)}
              />
              <Label htmlFor="autoPublish" className="text-sm">
                Publier automatiquement après génération
              </Label>
            </div>
          </div>

          {/* Informations sur l'audit */}
          {audit && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <h4 className="font-medium text-sm mb-2">Informations de l'audit</h4>
              <div className="space-y-1 text-xs text-gray-600">
                <div>Titre: {audit.title}</div>
                <div>Organisation: {audit.organization.name}</div>
                <div>Statut: {audit.status}</div>
                <div>Observations: {audit._count.observations}</div>
                <div>Actions: {audit._count.actions}</div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Annuler
          </Button>
          <Button
            onClick={handleGenerate}
            disabled={loading || !audit}
            className="flex items-center gap-2"
          >
            {loading ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                Génération...
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4" />
                Générer
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
