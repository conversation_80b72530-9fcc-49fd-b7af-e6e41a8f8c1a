"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { ReportForm } from "@/components/features/reports"
import { useReportActions } from "@/hooks/use-report-actions"
import { useReportPermissions } from "@/hooks/use-report-permissions"
import { useAudits } from "@/hooks/use-audits"
import { CreateReportInput } from "@/lib/validations/report"
import { AlertTriangle, ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function NewReportPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { canCreate } = useReportPermissions()
  
  const {
    createReport,
    loading: actionLoading,
    error: actionError
  } = useReportActions()

  const {
    audits,
    loading: auditsLoading,
    error: auditsError
  } = useAudits({
    initialFilters: {
      page: 1,
      limit: 100,
      sortBy: "createdAt",
      sortOrder: "desc"
    }
  })

  const handleSubmit = async (data: CreateReportInput) => {
    try {
      setIsSubmitting(true)
      const report = await createReport(data)
      
      if (report) {
        router.push(`/reports/${report.id}`)
      }
    } catch (error) {
      console.error("Erreur lors de la création du rapport:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!canCreate()) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Retour
          </Button>
          <h1 className="text-3xl font-bold magneto-title">Nouveau rapport</h1>
        </div>

        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Vous n'avez pas les permissions nécessaires pour créer des rapports.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (auditsLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Retour
          </Button>
          <h1 className="text-3xl font-bold magneto-title">Nouveau rapport</h1>
        </div>

        <div className="flex items-center justify-center py-8">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Retour
        </Button>
        <div>
          <h1 className="text-3xl font-bold magneto-title">Nouveau rapport</h1>
          <p className="text-gray-600">
            Créez un nouveau rapport d'audit
          </p>
        </div>
      </div>

      {/* Erreurs */}
      {(auditsError || actionError) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {auditsError || actionError}
          </AlertDescription>
        </Alert>
      )}

      {/* Formulaire */}
      <ReportForm
        audits={audits}
        onSubmit={handleSubmit}
        loading={isSubmitting || actionLoading}
        error={actionError}
      />
    </div>
  )
}
