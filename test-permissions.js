// Test script pour vérifier les permissions des rapports
const { hasPermission } = require('./src/lib/validations/user.ts');

// Test des permissions pour différents rôles
const roles = ['SUPER_ADMIN', 'ADMIN', 'MANAGER', 'AUDITOR', 'USER'];

console.log('=== Test des permissions reports:read ===\n');

roles.forEach(role => {
  const canRead = hasPermission(role, 'reports', 'read');
  console.log(`${role}: ${canRead ? '✅ Peut lire les rapports' : '❌ Ne peut pas lire les rapports'}`);
});

console.log('\n=== Test des permissions reports:create ===\n');

roles.forEach(role => {
  const canCreate = hasPermission(role, 'reports', 'create');
  console.log(`${role}: ${canCreate ? '✅ Peut créer des rapports' : '❌ Ne peut pas créer des rapports'}`);
});

console.log('\n=== Test des permissions reports:update ===\n');

roles.forEach(role => {
  const canUpdate = hasPermission(role, 'reports', 'update');
  console.log(`${role}: ${canUpdate ? '✅ Peut modifier des rapports' : '❌ Ne peut pas modifier des rapports'}`);
});
