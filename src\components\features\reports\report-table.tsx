"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ReportStatusBadge } from "./report-status-badge"
import { ReportTypeBadge } from "./report-type-badge"
import { ReportWithRelations } from "@/lib/validations/report"
import { Eye, Edit, Trash2, Download, Share, FileText } from "lucide-react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface ReportTableProps {
  reports: ReportWithRelations[]
  onView?: (report: ReportWithRelations) => void
  onEdit?: (report: ReportWithRelations) => void
  onDelete?: (report: ReportWithRelations) => void
  onExport?: (report: ReportWithRelations) => void
  onPublish?: (report: ReportWithRelations) => void
  loading?: boolean
  showAuditInfo?: boolean
  showOrganizationInfo?: boolean
}

export function ReportTable({
  reports,
  onView,
  onEdit,
  onDelete,
  onExport,
  onPublish,
  loading = false,
  showAuditInfo = true,
  showOrganizationInfo = true
}: ReportTableProps) {
  const formatDate = (date: Date) => {
    return format(date, "dd/MM/yyyy", { locale: fr })
  }

  const formatDateTime = (date: Date) => {
    return format(date, "dd/MM/yyyy HH:mm", { locale: fr })
  }

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-100 rounded animate-pulse" />
        ))}
      </div>
    )
  }

  if (reports.length === 0) {
    return (
      <div className="text-center py-8">
        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun rapport trouvé</h3>
        <p className="text-gray-500">
          Il n'y a aucun rapport correspondant à vos critères de recherche.
        </p>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Titre</TableHead>
            <TableHead>Statut</TableHead>
            {showAuditInfo && <TableHead>Audit</TableHead>}
            {showOrganizationInfo && <TableHead>Organisation</TableHead>}
            <TableHead>Créateur</TableHead>
            <TableHead>Créé le</TableHead>
            <TableHead>Publié le</TableHead>
            <TableHead>Version</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {reports.map((report) => {
            const canEdit = report.status === "DRAFT" || report.status === "IN_REVIEW"
            const canPublish = report.status === "APPROVED"
            const isPublished = report.status === "PUBLISHED"

            return (
              <TableRow key={report.id}>
                <TableCell className="font-medium">
                  <div className="space-y-1">
                    <div className="font-medium text-gray-900 line-clamp-1">
                      {report.title}
                    </div>
                    {report.type && (
                      <ReportTypeBadge type={report.type} className="text-xs" />
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <ReportStatusBadge status={report.status} />
                </TableCell>

                {showAuditInfo && (
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium line-clamp-1">
                        {report.audit.title}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {report.audit.status}
                      </Badge>
                    </div>
                  </TableCell>
                )}

                {showOrganizationInfo && (
                  <TableCell>
                    <span className="text-sm">{report.organization.name}</span>
                  </TableCell>
                )}

                <TableCell>
                  <span className="text-sm">
                    {report.creator.name || report.creator.email}
                  </span>
                </TableCell>

                <TableCell>
                  <span className="text-sm text-gray-600">
                    {formatDate(report.createdAt)}
                  </span>
                </TableCell>

                <TableCell>
                  {report.publishedAt ? (
                    <span className="text-sm text-green-600">
                      {formatDateTime(report.publishedAt)}
                    </span>
                  ) : (
                    <span className="text-sm text-gray-400">-</span>
                  )}
                </TableCell>

                <TableCell>
                  <Badge variant="outline" className="text-xs">
                    v{report.version}
                  </Badge>
                </TableCell>

                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-1">
                    {onView && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onView(report)}
                        className="h-8 w-8 p-0"
                        title="Voir le rapport"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}

                    {onEdit && canEdit && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEdit(report)}
                        className="h-8 w-8 p-0"
                        title="Modifier le rapport"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    )}

                    {onExport && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onExport(report)}
                        className="h-8 w-8 p-0"
                        title="Exporter le rapport"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    )}

                    {onPublish && canPublish && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onPublish(report)}
                        className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
                        title="Publier le rapport"
                      >
                        <Share className="h-4 w-4" />
                      </Button>
                    )}

                    {onDelete && !isPublished && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDelete(report)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        title="Supprimer le rapport"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
