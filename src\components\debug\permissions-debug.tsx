"use client"

import { useSession } from "@/lib/auth/client"
import { useReportPermissions } from "@/hooks/use-report-permissions"
import { hasPermission } from "@/lib/validations/user"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"

export function PermissionsDebug() {
  const { data: session, error } = useSession()
  const { canRead, canCreate, canUpdate, canDelete } = useReportPermissions()

  if (!session?.user) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardHeader>
          <CardTitle className="text-red-800">🔍 Debug Permissions - Pas de session</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600">
            {error ? `Erreur: ${error}` : "Aucune session utilisateur trouvée"}
          </p>
        </CardContent>
      </Card>
    )
  }

  const userRole = session.user.role as UserRole
  const permissions = [
    { name: "reports:read", value: hasPermission(userRole, "reports", "read") },
    { name: "reports:create", value: hasPermission(userRole, "reports", "create") },
    { name: "reports:update", value: hasPermission(userRole, "reports", "update") },
    { name: "reports:delete", value: hasPermission(userRole, "reports", "delete") },
  ]

  const hookPermissions = [
    { name: "canRead()", value: canRead() },
    { name: "canCreate()", value: canCreate() },
    { name: "canUpdate()", value: canUpdate() },
    { name: "canDelete()", value: canDelete() },
  ]

  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardHeader>
        <CardTitle className="text-blue-800">🔍 Debug Permissions</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-semibold text-blue-700">Informations utilisateur:</h3>
          <p><strong>ID:</strong> {session.user.id}</p>
          <p><strong>Email:</strong> {session.user.email}</p>
          <p><strong>Rôle:</strong> <Badge variant="outline">{session.user.role}</Badge></p>
          <p><strong>Organisation:</strong> {session.user.organizationId || "Aucune"}</p>
          <p><strong>Actif:</strong> {session.user.isActive ? "✅" : "❌"}</p>
        </div>

        <div>
          <h3 className="font-semibold text-blue-700">Permissions de base (hasPermission):</h3>
          <div className="grid grid-cols-2 gap-2">
            {permissions.map((perm) => (
              <div key={perm.name} className="flex items-center justify-between">
                <span className="text-sm">{perm.name}:</span>
                <Badge variant={perm.value ? "default" : "destructive"}>
                  {perm.value ? "✅" : "❌"}
                </Badge>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="font-semibold text-blue-700">Permissions du hook (useReportPermissions):</h3>
          <div className="grid grid-cols-2 gap-2">
            {hookPermissions.map((perm) => (
              <div key={perm.name} className="flex items-center justify-between">
                <span className="text-sm">{perm.name}:</span>
                <Badge variant={perm.value ? "default" : "destructive"}>
                  {perm.value ? "✅" : "❌"}
                </Badge>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
