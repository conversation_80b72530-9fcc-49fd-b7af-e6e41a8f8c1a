"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createReportSchema, updateReportSchema, CreateReportInput, UpdateReportInput, ReportType, ReportWithRelations } from "@/lib/validations/report"
import { AuditWithRelations } from "@/lib/validations/audit"
import { AlertTriangle, Save, FileText } from "lucide-react"

interface ReportFormProps {
  report?: ReportWithRelations
  audits: AuditWithRelations[]
  onSubmit: (data: CreateReportInput | UpdateReportInput) => Promise<void>
  loading?: boolean
  error?: string | null
}

export function ReportForm({
  report,
  audits,
  onSubmit,
  loading = false,
  error
}: ReportFormProps) {
  const isEditing = !!report
  const schema = isEditing ? updateReportSchema : createReportSchema

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<CreateReportInput | UpdateReportInput>({
    resolver: zodResolver(schema),
    defaultValues: isEditing ? {
      title: report.title,
      content: report.content,
      type: report.type,
    } : {
      type: "AUDIT_SUMMARY" as ReportType,
      includeObservations: true,
      includeActions: true,
      includeStatistics: true
    }
  })

  const [selectedAuditId, setSelectedAuditId] = useState<string>(
    isEditing ? report.auditId : ""
  )

  const selectedAudit = audits.find(audit => audit.id === selectedAuditId)

  useEffect(() => {
    if (selectedAuditId && !isEditing) {
      setValue("auditId", selectedAuditId)
      if (selectedAudit) {
        setValue("organizationId", selectedAudit.organizationId)
      }
    }
  }, [selectedAuditId, selectedAudit, setValue, isEditing])

  const handleFormSubmit = async (data: CreateReportInput | UpdateReportInput) => {
    await onSubmit(data)
  }

  const reportTypes = [
    { value: "AUDIT_SUMMARY", label: "Résumé d'audit" },
    { value: "DETAILED_FINDINGS", label: "Constats détaillés" },
    { value: "ACTION_PLAN", label: "Plan d'action" },
    { value: "COMPLIANCE_REPORT", label: "Rapport de conformité" },
    { value: "EXECUTIVE_SUMMARY", label: "Résumé exécutif" }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          {isEditing ? "Modifier le rapport" : "Créer un nouveau rapport"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Sélection de l'audit (uniquement en création) */}
          {!isEditing && (
            <div className="space-y-2">
              <Label htmlFor="audit">Audit *</Label>
              <Select
                value={selectedAuditId}
                onValueChange={setSelectedAuditId}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionnez un audit" />
                </SelectTrigger>
                <SelectContent>
                  {audits.map((audit) => (
                    <SelectItem key={audit.id} value={audit.id}>
                      <div>
                        <div className="font-medium">{audit.title}</div>
                        <div className="text-sm text-gray-500">{audit.organization.name}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.auditId && (
                <p className="text-sm text-red-600">{errors.auditId.message}</p>
              )}
            </div>
          )}

          {/* Informations sur l'audit sélectionné */}
          {selectedAudit && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <h4 className="font-medium text-sm mb-2">Audit sélectionné</h4>
              <div className="space-y-1 text-sm text-gray-600">
                <div>Titre: {selectedAudit.title}</div>
                <div>Organisation: {selectedAudit.organization.name}</div>
                <div>Statut: {selectedAudit.status}</div>
                <div>Observations: {selectedAudit._count.observations}</div>
                <div>Actions: {selectedAudit._count.actions}</div>
              </div>
            </div>
          )}

          {/* Type de rapport */}
          <div className="space-y-2">
            <Label htmlFor="type">Type de rapport</Label>
            <Select
              value={watch("type") || "AUDIT_SUMMARY"}
              onValueChange={(value) => setValue("type", value as ReportType)}
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {reportTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.type && (
              <p className="text-sm text-red-600">{errors.type.message}</p>
            )}
          </div>

          {/* Titre */}
          <div className="space-y-2">
            <Label htmlFor="title">Titre du rapport *</Label>
            <Input
              id="title"
              {...register("title")}
              placeholder="Entrez le titre du rapport"
              disabled={loading}
            />
            {errors.title && (
              <p className="text-sm text-red-600">{errors.title.message}</p>
            )}
          </div>

          {/* Contenu */}
          <div className="space-y-2">
            <Label htmlFor="content">Contenu du rapport *</Label>
            <Textarea
              id="content"
              {...register("content")}
              placeholder="Rédigez le contenu du rapport..."
              rows={12}
              disabled={loading}
              className="font-mono text-sm"
            />
            <p className="text-xs text-gray-500">
              Vous pouvez utiliser la syntaxe Markdown pour formater le contenu.
            </p>
            {errors.content && (
              <p className="text-sm text-red-600">{errors.content.message}</p>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-3 pt-4 border-t">
            <Button
              type="submit"
              disabled={loading || (!selectedAuditId && !isEditing)}
              className="flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  {isEditing ? "Mise à jour..." : "Création..."}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  {isEditing ? "Mettre à jour" : "Créer le rapport"}
                </>
              )}
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={() => reset()}
              disabled={loading}
            >
              Réinitialiser
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
