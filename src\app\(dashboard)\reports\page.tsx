"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  ReportTable,
  ReportStatsCards,
  ReportFilters,
  ReportExportDialog,
  ReportGenerateDialog
} from "@/components/features/reports"
import { useReports, useReportStats } from "@/hooks/use-reports"
import { useReportActions } from "@/hooks/use-report-actions"
import { useReportPermissions } from "@/hooks/use-report-permissions"
import { useAudits } from "@/hooks/use-audits"
import { ReportFilters as ReportFiltersType, ReportWithRelations, ExportReportInput, GenerateReportInput } from "@/lib/validations/report"
import { AuditWithRelations } from "@/lib/validations/audit"
import { Plus, FileText, Filter, AlertTriangle, Wand2, Download } from "lucide-react"
import { useRouter } from "next/navigation"

export default function ReportsPage() {
  const router = useRouter()
  const [view, setView] = useState<"table" | "cards">("table")
  const [exportDialogOpen, setExportDialogOpen] = useState(false)
  const [generateDialogOpen, setGenerateDialogOpen] = useState(false)
  const [selectedReport, setSelectedReport] = useState<ReportWithRelations | null>(null)
  const [selectedAudit, setSelectedAudit] = useState<AuditWithRelations | null>(null)

  const {
    canRead,
    canCreate,
    canUpdate,
    canDelete,
    canExport,
    canPublish,
    canGenerate
  } = useReportPermissions()

  const {
    reports,
    loading,
    error,
    total,
    page,
    totalPages,
    refetch,
    setFilters,
    filters
  } = useReports({
    initialFilters: {
      page: 1,
      limit: 20,
      sortBy: "createdAt",
      sortOrder: "desc"
    }
  })

  const {
    stats,
    loading: statsLoading,
    error: statsError
  } = useReportStats()

  const {
    audits: availableAudits,
    loading: auditsLoading
  } = useAudits({
    initialFilters: {
      page: 1,
      limit: 100,
      sortBy: "createdAt",
      sortOrder: "desc"
    }
  })

  const {
    loading: actionLoading,
    error: actionError,
    exportReport,
    generateReport,
    deleteReport,
    publishReport
  } = useReportActions()

  // Handlers pour les actions
  const handleViewReport = (report: ReportWithRelations) => {
    router.push(`/reports/${report.id}`)
  }

  const handleEditReport = (report: ReportWithRelations) => {
    router.push(`/reports/${report.id}/edit`)
  }

  const handleDeleteReport = async (report: ReportWithRelations) => {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le rapport "${report.title}" ?`)) {
      const success = await deleteReport(report.id)
      if (success) {
        await refetch()
      }
    }
  }

  const handleExportReport = (report: ReportWithRelations) => {
    setSelectedReport(report)
    setExportDialogOpen(true)
  }

  const handlePublishReport = async (report: ReportWithRelations) => {
    if (confirm(`Publier le rapport "${report.title}" ?`)) {
      const published = await publishReport(report.id, { notifyStakeholders: true })
      if (published) {
        await refetch()
      }
    }
  }

  const handleGenerateReport = (audit: AuditWithRelations) => {
    setSelectedAudit(audit)
    setGenerateDialogOpen(true)
  }

  // Handlers pour les filtres
  const handleFiltersChange = (newFilters: Partial<ReportFiltersType>) => {
    setFilters(newFilters)
  }

  const handleFiltersReset = () => {
    setFilters({
      page: 1,
      limit: 20,
      sortBy: "createdAt",
      sortOrder: "desc"
    })
  }

  const handlePageChange = (newPage: number) => {
    setFilters({ page: newPage })
  }

  // Handlers pour les dialogues
  const handleExportSubmit = async (exportData: ExportReportInput) => {
    if (!selectedReport) return

    const blob = await exportReport(selectedReport.id, exportData)
    if (blob) {
      // Télécharger le fichier
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${selectedReport.title}.${exportData.format.toLowerCase()}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    }
  }

  const handleGenerateSubmit = async (generateData: GenerateReportInput) => {
    const generated = await generateReport(generateData)
    if (generated) {
      await refetch()
    }
  }

  if (!canRead()) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Vous n'avez pas les permissions nécessaires pour voir les rapports.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold magneto-title">Gestion des rapports</h1>
          <p className="text-gray-600">
            Créez, gérez et exportez vos rapports d'audit
          </p>
        </div>

        <div className="flex items-center gap-2">
          {canGenerate() && (
            <Button
              onClick={() => setGenerateDialogOpen(true)}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Wand2 className="h-4 w-4" />
              Générer
            </Button>
          )}

          {canCreate() && (
            <Button
              onClick={() => router.push("/reports/new")}
              className="magneto-button flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Nouveau rapport
            </Button>
          )}
        </div>
      </div>

      {/* Statistiques */}
      {stats && (
        <ReportStatsCards stats={stats} loading={statsLoading} />
      )}

      {/* Filtres et recherche */}
      <ReportFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onReset={handleFiltersReset}
        loading={loading}
      />

      {/* Erreurs */}
      {(error || actionError) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error || actionError}
          </AlertDescription>
        </Alert>
      )}

      {/* Liste des rapports */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>
              Rapports ({total})
            </CardTitle>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">Vue:</span>
              <Button
                variant={view === "table" ? "default" : "outline"}
                size="sm"
                onClick={() => setView("table")}
              >
                Tableau
              </Button>
              <Button
                variant={view === "cards" ? "default" : "outline"}
                size="sm"
                onClick={() => setView("cards")}
              >
                Cartes
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ReportTable
            reports={reports}
            onView={handleViewReport}
            onEdit={canUpdate() ? handleEditReport : undefined}
            onDelete={canDelete() ? handleDeleteReport : undefined}
            onExport={canExport() ? handleExportReport : undefined}
            onPublish={canPublish() ? handlePublishReport : undefined}
            loading={loading || actionLoading}
            showAuditInfo={true}
            showOrganizationInfo={true}
          />

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(page - 1)}
                disabled={page <= 1}
              >
                Précédent
              </Button>

              <span className="text-sm text-gray-500">
                Page {page} sur {totalPages}
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(page + 1)}
                disabled={page >= totalPages}
              >
                Suivant
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialogues */}
      <ReportExportDialog
        open={exportDialogOpen}
        onOpenChange={setExportDialogOpen}
        report={selectedReport}
        onExport={handleExportSubmit}
        loading={actionLoading}
      />

      <ReportGenerateDialog
        open={generateDialogOpen}
        onOpenChange={setGenerateDialogOpen}
        audit={selectedAudit}
        onGenerate={handleGenerateSubmit}
        loading={actionLoading}
      />
    </div>
  )
}
