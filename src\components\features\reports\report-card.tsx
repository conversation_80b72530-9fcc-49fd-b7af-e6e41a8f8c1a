import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ReportStatusBadge } from "./report-status-badge"
import { ReportTypeBadge } from "./report-type-badge"
import { ReportWithRelations } from "@/lib/validations/report"
import { Calendar, User, FileText, Eye, Edit, Trash2, Download, Share } from "lucide-react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface ReportCardProps {
  report: ReportWithRelations
  onView?: (report: ReportWithRelations) => void
  onEdit?: (report: ReportWithRelations) => void
  onDelete?: (report: ReportWithRelations) => void
  onExport?: (report: ReportWithRelations) => void
  onPublish?: (report: ReportWithRelations) => void
  showActions?: boolean
}

export function ReportCard({ 
  report, 
  onView, 
  onEdit, 
  onDelete, 
  onExport,
  onPublish,
  showActions = true 
}: ReportCardProps) {
  const formatDate = (date: Date) => {
    return format(date, "dd MMM yyyy", { locale: fr })
  }

  const formatDateTime = (date: Date) => {
    return format(date, "dd MMM yyyy à HH:mm", { locale: fr })
  }

  const canEdit = report.status === "DRAFT" || report.status === "IN_REVIEW"
  const canPublish = report.status === "APPROVED"
  const isPublished = report.status === "PUBLISHED"

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 line-clamp-2">
              {report.title}
            </CardTitle>
            <CardDescription className="text-sm text-gray-600">
              Audit: {report.audit.title}
            </CardDescription>
          </div>
          <div className="flex flex-col gap-2 items-end">
            <ReportStatusBadge status={report.status} />
            {report.type && <ReportTypeBadge type={report.type} />}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Informations principales */}
        <div className="grid grid-cols-1 gap-3 text-sm">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span className="text-gray-700">
              Créé le {formatDate(report.createdAt)}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-gray-500" />
            <span className="text-gray-700">
              Par {report.creator.name || report.creator.email}
            </span>
          </div>

          {report.publishedAt && (
            <div className="flex items-center gap-2">
              <Share className="h-4 w-4 text-green-500" />
              <span className="text-gray-700">
                Publié le {formatDateTime(report.publishedAt)}
              </span>
            </div>
          )}
        </div>

        {/* Organisation */}
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            {report.organization.name}
          </Badge>
          <Badge variant="outline" className="text-xs">
            Version {report.version}
          </Badge>
        </div>

        {/* Aperçu du contenu */}
        <div className="bg-gray-50 p-3 rounded-md">
          <p className="text-sm text-gray-700 line-clamp-3">
            {report.content.replace(/[#*]/g, '').substring(0, 150)}...
          </p>
        </div>

        {/* Informations sur l'audit */}
        <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded">
          <div className="flex items-center justify-between">
            <span>Audit: {report.audit.status}</span>
            <span>
              {formatDate(report.audit.startDate)}
              {report.audit.endDate && ` - ${formatDate(report.audit.endDate)}`}
            </span>
          </div>
        </div>

        {/* Actions */}
        {showActions && (
          <div className="flex items-center gap-2 pt-2 border-t flex-wrap">
            {onView && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onView(report)}
                className="flex items-center gap-1"
              >
                <Eye className="h-3 w-3" />
                Voir
              </Button>
            )}
            
            {onEdit && canEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(report)}
                className="flex items-center gap-1"
              >
                <Edit className="h-3 w-3" />
                Modifier
              </Button>
            )}

            {onExport && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onExport(report)}
                className="flex items-center gap-1"
              >
                <Download className="h-3 w-3" />
                Exporter
              </Button>
            )}

            {onPublish && canPublish && (
              <Button
                variant="default"
                size="sm"
                onClick={() => onPublish(report)}
                className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
              >
                <Share className="h-3 w-3" />
                Publier
              </Button>
            )}
            
            {onDelete && !isPublished && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(report)}
                className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-3 w-3" />
                Supprimer
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
