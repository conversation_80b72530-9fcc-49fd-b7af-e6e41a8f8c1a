"use client"

import { useSession } from "@/lib/auth/client"
import { hasPermission } from "@/lib/validations/user"
import { ReportWithRelations, isReportEditable, isReportPublishable, canArchiveReport } from "@/lib/validations/report"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"

// Hook pour vérifier les permissions de rapport
export function useReportPermissions(report?: ReportWithRelations) {
  const { data: session } = useSession()

  const checkPermission = (permission: string): boolean => {
    if (!session?.user) return false

    const [resource, action] = permission.split(':')
    const userRole = session.user.role as UserRole

    return hasPermission(userRole, resource, action)
  }

  const checkReportAccess = (reportToCheck?: ReportWithRelations): boolean => {
    const targetReport = reportToCheck || report
    if (!targetReport || !session?.user) return false

    // Les super admins ont accès à tout
    if (session.user.role === "SUPER_ADMIN") return true

    // Vérifier l'accès organisationnel
    if (session.user.organizationId !== targetReport.organizationId) return false

    // Les auditeurs ne peuvent voir que leurs propres rapports
    if (session.user.role === "AUDITOR") {
      return targetReport.creatorId === session.user.id
    }

    return true
  }

  return {
    canRead: (reportToCheck?: ReportWithRelations) => {
      // Si aucun rapport spécifique n'est fourni, vérifier seulement la permission de base
      if (!reportToCheck) {
        return checkPermission("reports:read")
      }
      // Si un rapport spécifique est fourni, vérifier la permission ET l'accès au rapport
      return checkPermission("reports:read") && checkReportAccess(reportToCheck)
    },

    canCreate: () => checkPermission("reports:create"),

    canUpdate: (reportToCheck?: ReportWithRelations) => {
      if (!checkPermission("reports:update")) return false

      const targetReport = reportToCheck || report
      if (!targetReport) return true

      // Vérifier l'accès au rapport
      if (!checkReportAccess(targetReport)) return false

      // Vérifier si le rapport est modifiable selon son statut
      if (!isReportEditable(targetReport.status)) return false

      // Les super admins peuvent tout modifier
      if (session?.user?.role === "SUPER_ADMIN") return true

      // Les autres peuvent modifier leurs propres rapports
      return targetReport.creatorId === session?.user?.id
    },

    canDelete: (reportToCheck?: ReportWithRelations) => {
      if (!checkPermission("reports:delete")) return false

      const targetReport = reportToCheck || report
      if (!targetReport) return true

      // Vérifier l'accès au rapport
      if (!checkReportAccess(targetReport)) return false

      // Seuls les admins et managers peuvent supprimer
      if (!["SUPER_ADMIN", "ADMIN", "MANAGER"].includes(session?.user?.role || "")) {
        return false
      }

      // Les super admins peuvent tout supprimer
      if (session?.user?.role === "SUPER_ADMIN") return true

      // Les autres peuvent supprimer leurs propres rapports ou ceux de leur organisation
      return targetReport.creatorId === session?.user?.id ||
             targetReport.organizationId === session?.user?.organizationId
    },

    canGenerate: () => checkPermission("reports:create"),

    canExport: (reportToCheck?: ReportWithRelations) => {
      return checkPermission("reports:read") && checkReportAccess(reportToCheck)
    },

    canPublish: (reportToCheck?: ReportWithRelations) => {
      const targetReport = reportToCheck || report
      if (!targetReport) return false

      // Seuls les managers et admins peuvent publier
      if (!["SUPER_ADMIN", "ADMIN", "MANAGER"].includes(session?.user?.role || "")) {
        return false
      }

      // Vérifier l'accès au rapport
      if (!checkReportAccess(targetReport)) return false

      // Vérifier si le rapport peut être publié selon son statut
      return isReportPublishable(targetReport.status)
    },

    canArchive: (reportToCheck?: ReportWithRelations) => {
      const targetReport = reportToCheck || report
      if (!targetReport) return false

      // Seuls les managers et admins peuvent archiver
      if (!["SUPER_ADMIN", "ADMIN", "MANAGER"].includes(session?.user?.role || "")) {
        return false
      }

      // Vérifier l'accès au rapport
      if (!checkReportAccess(targetReport)) return false

      // Vérifier si le rapport peut être archivé selon son statut
      return canArchiveReport(targetReport.status)
    },

    canViewStats: () => checkPermission("reports:read"),

    canManageTemplates: () => {
      return ["SUPER_ADMIN", "ADMIN", "MANAGER"].includes(session?.user?.role || "")
    },

    user: session?.user || null,
    
    // Utilitaires pour l'interface
    isOwner: (reportToCheck?: ReportWithRelations) => {
      const targetReport = reportToCheck || report
      return targetReport?.creatorId === session?.user?.id
    },

    isSameOrganization: (reportToCheck?: ReportWithRelations) => {
      const targetReport = reportToCheck || report
      return targetReport?.organizationId === session?.user?.organizationId
    },

    canEditContent: (reportToCheck?: ReportWithRelations) => {
      const targetReport = reportToCheck || report
      if (!targetReport) return false
      
      return isReportEditable(targetReport.status) && 
             (session?.user?.role === "SUPER_ADMIN" || targetReport.creatorId === session?.user?.id)
    }
  }
}
