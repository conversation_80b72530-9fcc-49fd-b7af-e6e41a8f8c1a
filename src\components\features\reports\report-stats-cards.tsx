import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { ReportStats, ReportStatus, ReportType } from "@/lib/validations/report"
import { FileText, TrendingUp, Calendar, Archive, Download, Eye } from "lucide-react"

interface ReportStatsCardsProps {
  stats: ReportStats
  loading?: boolean
}

export function ReportStatsCards({ stats, loading }: ReportStatsCardsProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="h-16 bg-gray-200 rounded animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const completionRate = stats.total > 0 ? (stats.published / stats.total) * 100 : 0
  const completionRateColor = completionRate >= 80 ? "text-green-600" : 
                             completionRate >= 60 ? "text-yellow-600" : "text-red-600"

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Total des rapports */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total des rapports</CardTitle>
          <FileText className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
          <p className="text-xs text-muted-foreground">
            {stats.published} publiés
          </p>
        </CardContent>
      </Card>

      {/* Rapports publiés */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Publiés</CardTitle>
          <Eye className="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{stats.published}</div>
          <p className="text-xs text-muted-foreground">
            Rapports disponibles
          </p>
        </CardContent>
      </Card>

      {/* Brouillons */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Brouillons</CardTitle>
          <Archive className="h-4 w-4 text-yellow-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">{stats.draft}</div>
          <p className="text-xs text-muted-foreground">
            En cours de rédaction
          </p>
        </CardContent>
      </Card>

      {/* Taux de publication */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Taux de publication</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${completionRateColor}`}>
            {completionRate.toFixed(1)}%
          </div>
          <p className="text-xs text-muted-foreground">
            Rapports finalisés
          </p>
        </CardContent>
      </Card>

      {/* Répartition par statut */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium">Répartition par statut</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-gray-400 rounded-full" />
                <span className="text-sm">Brouillons</span>
              </div>
              <span className="text-sm font-medium">{stats.byStatus[ReportStatus.DRAFT] || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-yellow-400 rounded-full" />
                <span className="text-sm">En révision</span>
              </div>
              <span className="text-sm font-medium">{stats.byStatus[ReportStatus.IN_REVIEW] || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-400 rounded-full" />
                <span className="text-sm">Approuvés</span>
              </div>
              <span className="text-sm font-medium">{stats.byStatus[ReportStatus.APPROVED] || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-400 rounded-full" />
                <span className="text-sm">Publiés</span>
              </div>
              <span className="text-sm font-medium">{stats.byStatus[ReportStatus.PUBLISHED] || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-400 rounded-full" />
                <span className="text-sm">Archivés</span>
              </div>
              <span className="text-sm font-medium">{stats.byStatus[ReportStatus.ARCHIVED] || 0}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Métriques de performance */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium">Métriques de performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">{stats.averageGenerationTime}min</div>
              <p className="text-xs text-muted-foreground">Temps moyen de génération</p>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-purple-600">{stats.totalExports}</div>
              <p className="text-xs text-muted-foreground">Exports réalisés</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
