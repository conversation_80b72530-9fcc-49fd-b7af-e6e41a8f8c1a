import { Badge } from "@/components/ui/badge"
import { ReportTypeType, getReportTypeLabel } from "@/lib/validations/report"

interface ReportTypeBadgeProps {
  type: ReportTypeType
  className?: string
}

export function ReportTypeBadge({ type, className }: ReportTypeBadgeProps) {
  const getTypeColor = (type: ReportTypeType): string => {
    const colors = {
      AUDIT_SUMMARY: "bg-blue-100 text-blue-800",
      DETAILED_FINDINGS: "bg-purple-100 text-purple-800",
      ACTION_PLAN: "bg-orange-100 text-orange-800",
      COMPLIANCE_REPORT: "bg-green-100 text-green-800",
      EXECUTIVE_SUMMARY: "bg-indigo-100 text-indigo-800"
    }
    return colors[type] || "bg-gray-100 text-gray-800"
  }

  return (
    <Badge 
      variant="secondary" 
      className={`${getTypeColor(type)} ${className}`}
    >
      {getReportTypeLabel(type)}
    </Badge>
  )
}
