"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ReportFilters as ReportFiltersType, ReportStatus, ReportType } from "@/lib/validations/report"
import { Search, Filter, X } from "lucide-react"

interface ReportFiltersProps {
  filters: ReportFiltersType
  onFiltersChange: (filters: Partial<ReportFiltersType>) => void
  onReset: () => void
  loading?: boolean
}

export function ReportFilters({
  filters,
  onFiltersChange,
  onReset,
  loading = false
}: ReportFiltersProps) {
  const [searchTerm, setSearchTerm] = useState(filters.search || "")

  const handleSearch = () => {
    onFiltersChange({ search: searchTerm, page: 1 })
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const handleStatusChange = (status: string) => {
    onFiltersChange({ 
      status: status === "all" ? undefined : status as ReportStatus,
      page: 1 
    })
  }

  const handleTypeChange = (type: string) => {
    onFiltersChange({ 
      type: type === "all" ? undefined : type as ReportType,
      page: 1 
    })
  }

  const handleSortChange = (sortBy: string) => {
    onFiltersChange({ sortBy: sortBy as any, page: 1 })
  }

  const handleSortOrderChange = (sortOrder: string) => {
    onFiltersChange({ sortOrder: sortOrder as "asc" | "desc", page: 1 })
  }

  const hasActiveFilters = filters.search || filters.status || filters.type

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtres et recherche
          </CardTitle>
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={onReset}
              className="flex items-center gap-1"
            >
              <X className="h-4 w-4" />
              Réinitialiser
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Recherche */}
          <div className="flex gap-2">
            <Input
              placeholder="Rechercher un rapport..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={loading}
            />
            <Button 
              onClick={handleSearch} 
              variant="outline"
              disabled={loading}
            >
              <Search className="h-4 w-4" />
            </Button>
          </div>

          {/* Statut */}
          <Select
            value={filters.status || "all"}
            onValueChange={handleStatusChange}
            disabled={loading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Statut" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous les statuts</SelectItem>
              <SelectItem value={ReportStatus.DRAFT}>Brouillon</SelectItem>
              <SelectItem value={ReportStatus.IN_REVIEW}>En révision</SelectItem>
              <SelectItem value={ReportStatus.APPROVED}>Approuvé</SelectItem>
              <SelectItem value={ReportStatus.PUBLISHED}>Publié</SelectItem>
              <SelectItem value={ReportStatus.ARCHIVED}>Archivé</SelectItem>
            </SelectContent>
          </Select>

          {/* Type */}
          <Select
            value={filters.type || "all"}
            onValueChange={handleTypeChange}
            disabled={loading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous les types</SelectItem>
              <SelectItem value={ReportType.AUDIT_SUMMARY}>Résumé d'audit</SelectItem>
              <SelectItem value={ReportType.DETAILED_FINDINGS}>Constats détaillés</SelectItem>
              <SelectItem value={ReportType.ACTION_PLAN}>Plan d'action</SelectItem>
              <SelectItem value={ReportType.COMPLIANCE_REPORT}>Rapport de conformité</SelectItem>
              <SelectItem value={ReportType.EXECUTIVE_SUMMARY}>Résumé exécutif</SelectItem>
            </SelectContent>
          </Select>

          {/* Tri */}
          <Select
            value={filters.sortBy || "createdAt"}
            onValueChange={handleSortChange}
            disabled={loading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Trier par" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="title">Titre</SelectItem>
              <SelectItem value="status">Statut</SelectItem>
              <SelectItem value="type">Type</SelectItem>
              <SelectItem value="createdAt">Date de création</SelectItem>
              <SelectItem value="publishedAt">Date de publication</SelectItem>
              <SelectItem value="version">Version</SelectItem>
            </SelectContent>
          </Select>

          {/* Ordre de tri */}
          <Select
            value={filters.sortOrder || "desc"}
            onValueChange={handleSortOrderChange}
            disabled={loading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Ordre" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="asc">Croissant</SelectItem>
              <SelectItem value="desc">Décroissant</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Filtres actifs */}
        {hasActiveFilters && (
          <div className="flex items-center gap-2 mt-4 pt-4 border-t">
            <span className="text-sm text-gray-500">Filtres actifs:</span>
            {filters.search && (
              <div className="flex items-center gap-1 bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                <span>Recherche: "{filters.search}"</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-blue-200"
                  onClick={() => {
                    setSearchTerm("")
                    onFiltersChange({ search: undefined, page: 1 })
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            )}
            {filters.status && (
              <div className="flex items-center gap-1 bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                <span>Statut: {filters.status}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-green-200"
                  onClick={() => onFiltersChange({ status: undefined, page: 1 })}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            )}
            {filters.type && (
              <div className="flex items-center gap-1 bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">
                <span>Type: {filters.type}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-purple-200"
                  onClick={() => onFiltersChange({ type: undefined, page: 1 })}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
