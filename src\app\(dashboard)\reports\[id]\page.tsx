"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { ReportStatusBadge, ReportTypeBadge, ReportExportDialog } from "@/components/features/reports"
import { useReportActions } from "@/hooks/use-report-actions"
import { useReportPermissions } from "@/hooks/use-report-permissions"
import { ReportWithRelations, ExportReportInput } from "@/lib/validations/report"
import { ArrowLeft, Edit, Trash2, Download, Share, Eye, Calendar, User, Building } from "lucide-react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface ReportDetailPageProps {
  params: { id: string }
}

export default function ReportDetailPage({ params }: ReportDetailPageProps) {
  const router = useRouter()
  const [report, setReport] = useState<ReportWithRelations | null>(null)
  const [loading, setLoading] = useState(true)
  const [exportDialogOpen, setExportDialogOpen] = useState(false)

  const {
    getReport,
    deleteReport,
    publishReport,
    exportReport,
    loading: actionLoading,
    error: actionError
  } = useReportActions()

  const {
    canRead,
    canUpdate,
    canDelete,
    canExport,
    canPublish
  } = useReportPermissions(report || undefined)

  useEffect(() => {
    const fetchReport = async () => {
      try {
        setLoading(true)
        const fetchedReport = await getReport(params.id)
        setReport(fetchedReport)
      } catch (error) {
        console.error("Erreur lors du chargement du rapport:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchReport()
  }, [params.id, getReport])

  const handleEdit = () => {
    router.push(`/reports/${params.id}/edit`)
  }

  const handleDelete = async () => {
    if (!report) return
    
    if (confirm(`Êtes-vous sûr de vouloir supprimer le rapport "${report.title}" ?`)) {
      const success = await deleteReport(params.id)
      if (success) {
        router.push("/reports")
      }
    }
  }

  const handlePublish = async () => {
    if (!report) return
    
    if (confirm(`Publier le rapport "${report.title}" ?`)) {
      const published = await publishReport(params.id, { notifyStakeholders: true })
      if (published) {
        setReport(published)
      }
    }
  }

  const handleExport = () => {
    setExportDialogOpen(true)
  }

  const handleExportSubmit = async (exportData: ExportReportInput) => {
    if (!report) return

    const blob = await exportReport(report.id, exportData)
    if (blob) {
      // Télécharger le fichier
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${report.title}.${exportData.format.toLowerCase()}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    }
  }

  const formatDate = (date: Date) => {
    return format(date, "dd MMMM yyyy à HH:mm", { locale: fr })
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Retour
          </Button>
          <div className="h-8 bg-gray-200 rounded w-64 animate-pulse" />
        </div>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-100 rounded animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  if (!report) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Retour
          </Button>
          <h1 className="text-3xl font-bold magneto-title">Rapport non trouvé</h1>
        </div>

        <Alert variant="destructive">
          <AlertDescription>
            Le rapport demandé n'existe pas ou vous n'avez pas les permissions pour le consulter.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!canRead(report)) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Retour
          </Button>
          <h1 className="text-3xl font-bold magneto-title">Accès refusé</h1>
        </div>

        <Alert variant="destructive">
          <AlertDescription>
            Vous n'avez pas les permissions nécessaires pour consulter ce rapport.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  const canEdit = canUpdate(report)
  const canRemove = canDelete(report)
  const canPublishReport = canPublish(report)
  const canExportReport = canExport(report)

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title line-clamp-2">
              {report.title}
            </h1>
            <div className="flex items-center gap-2 mt-1">
              <ReportStatusBadge status={report.status} />
              {report.type && <ReportTypeBadge type={report.type} />}
              <Badge variant="outline">Version {report.version}</Badge>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {canExportReport && (
            <Button
              variant="outline"
              onClick={handleExport}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Exporter
            </Button>
          )}

          {canPublishReport && (
            <Button
              onClick={handlePublish}
              disabled={actionLoading}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
            >
              <Share className="h-4 w-4" />
              Publier
            </Button>
          )}

          {canEdit && (
            <Button
              onClick={handleEdit}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              Modifier
            </Button>
          )}

          {canRemove && (
            <Button
              onClick={handleDelete}
              variant="outline"
              disabled={actionLoading}
              className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4" />
              Supprimer
            </Button>
          )}
        </div>
      </div>

      {/* Erreurs */}
      {actionError && (
        <Alert variant="destructive">
          <AlertDescription>{actionError}</AlertDescription>
        </Alert>
      )}

      {/* Informations du rapport */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Contenu du rapport
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose max-w-none">
              <div className="whitespace-pre-wrap text-sm leading-relaxed">
                {report.content}
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-6">
          {/* Métadonnées */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informations</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Créé le</p>
                  <p className="font-medium">{formatDate(report.createdAt)}</p>
                </div>
              </div>

              {report.publishedAt && (
                <div className="flex items-center gap-2">
                  <Share className="h-4 w-4 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-500">Publié le</p>
                    <p className="font-medium text-green-600">{formatDate(report.publishedAt)}</p>
                  </div>
                </div>
              )}

              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Créateur</p>
                  <p className="font-medium">{report.creator.name || report.creator.email}</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Building className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Organisation</p>
                  <p className="font-medium">{report.organization.name}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informations sur l'audit */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Audit associé</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm text-gray-500">Titre</p>
                <p className="font-medium">{report.audit.title}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Statut</p>
                <Badge variant="outline">{report.audit.status}</Badge>
              </div>
              <div>
                <p className="text-sm text-gray-500">Période</p>
                <p className="text-sm">
                  {format(report.audit.startDate, "dd/MM/yyyy", { locale: fr })}
                  {report.audit.endDate && ` - ${format(report.audit.endDate, "dd/MM/yyyy", { locale: fr })}`}
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/audits/${report.audit.id}`)}
                className="w-full"
              >
                Voir l'audit
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Dialogue d'export */}
      <ReportExportDialog
        open={exportDialogOpen}
        onOpenChange={setExportDialogOpen}
        report={report}
        onExport={handleExportSubmit}
        loading={actionLoading}
      />
    </div>
  )
}
