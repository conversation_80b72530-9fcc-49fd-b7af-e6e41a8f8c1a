"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ReportForm } from "@/components/features/reports"
import { useReportActions } from "@/hooks/use-report-actions"
import { useReportPermissions } from "@/hooks/use-report-permissions"
import { useAudits } from "@/hooks/use-audits"
import { ReportWithRelations, UpdateReportInput } from "@/lib/validations/report"
import { ArrowLeft, AlertTriangle } from "lucide-react"

interface EditReportPageProps {
  params: { id: string }
}

export default function EditReportPage({ params }: EditReportPageProps) {
  const router = useRouter()
  const [report, setReport] = useState<ReportWithRelations | null>(null)
  const [loading, setLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    getReport,
    updateReport,
    loading: actionLoading,
    error: actionError
  } = useReportActions()

  const {
    canUpdate
  } = useReportPermissions(report || undefined)

  const {
    audits,
    loading: auditsLoading,
    error: auditsError
  } = useAudits({
    initialFilters: {
      page: 1,
      limit: 100,
      sortBy: "createdAt",
      sortOrder: "desc"
    }
  })

  useEffect(() => {
    const fetchReport = async () => {
      try {
        setLoading(true)
        const fetchedReport = await getReport(params.id)
        setReport(fetchedReport)
      } catch (error) {
        console.error("Erreur lors du chargement du rapport:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchReport()
  }, [params.id, getReport])

  const handleSubmit = async (data: UpdateReportInput) => {
    try {
      setIsSubmitting(true)
      const updatedReport = await updateReport(params.id, data)
      
      if (updatedReport) {
        router.push(`/reports/${params.id}`)
      }
    } catch (error) {
      console.error("Erreur lors de la mise à jour du rapport:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (loading || auditsLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Retour
          </Button>
          <div className="h-8 bg-gray-200 rounded w-64 animate-pulse" />
        </div>

        <div className="flex items-center justify-center py-8">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
        </div>
      </div>
    )
  }

  if (!report) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Retour
          </Button>
          <h1 className="text-3xl font-bold magneto-title">Rapport non trouvé</h1>
        </div>

        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Le rapport demandé n'existe pas ou vous n'avez pas les permissions pour le modifier.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!canUpdate(report)) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Retour
          </Button>
          <h1 className="text-3xl font-bold magneto-title">Modification du rapport</h1>
        </div>

        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Vous n'avez pas les permissions nécessaires pour modifier ce rapport ou le rapport ne peut plus être modifié.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Retour
        </Button>
        <div>
          <h1 className="text-3xl font-bold magneto-title">Modifier le rapport</h1>
          <p className="text-gray-600">
            {report.title}
          </p>
        </div>
      </div>

      {/* Erreurs */}
      {(auditsError || actionError) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {auditsError || actionError}
          </AlertDescription>
        </Alert>
      )}

      {/* Informations sur le statut */}
      {report.status !== "DRAFT" && report.status !== "IN_REVIEW" && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Ce rapport a le statut "{report.status}". Les modifications peuvent être limitées selon le statut.
          </AlertDescription>
        </Alert>
      )}

      {/* Formulaire */}
      <ReportForm
        report={report}
        audits={audits}
        onSubmit={handleSubmit}
        loading={isSubmitting || actionLoading}
        error={actionError}
      />
    </div>
  )
}
