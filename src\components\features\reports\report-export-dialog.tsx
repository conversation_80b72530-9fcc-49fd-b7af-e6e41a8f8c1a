"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { ExportReportInput, ExportFormat, ReportWithRelations } from "@/lib/validations/report"
import { Download, FileText, File, Table, Globe } from "lucide-react"

interface ReportExportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  report: ReportWithRelations | null
  onExport: (data: ExportReportInput) => Promise<void>
  loading?: boolean
}

export function ReportExportDialog({
  open,
  onOpenChange,
  report,
  onExport,
  loading = false
}: ReportExportDialogProps) {
  const [format, setFormat] = useState<ExportFormat>("PDF")
  const [includeAttachments, setIncludeAttachments] = useState(true)
  const [includeMetadata, setIncludeMetadata] = useState(false)

  const handleExport = async () => {
    if (!report) return

    const exportData: ExportReportInput = {
      format,
      includeAttachments,
      includeMetadata
    }

    await onExport(exportData)
    onOpenChange(false)
  }

  const formatOptions = [
    {
      value: "PDF" as ExportFormat,
      label: "PDF",
      description: "Document portable, idéal pour l'impression",
      icon: FileText
    },
    {
      value: "WORD" as ExportFormat,
      label: "Word",
      description: "Document modifiable Microsoft Word",
      icon: File
    },
    {
      value: "EXCEL" as ExportFormat,
      label: "Excel",
      description: "Feuille de calcul avec données structurées",
      icon: Table
    },
    {
      value: "HTML" as ExportFormat,
      label: "HTML",
      description: "Page web consultable dans un navigateur",
      icon: Globe
    }
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Exporter le rapport
          </DialogTitle>
          <DialogDescription>
            {report && (
              <>Exportez "{report.title}" dans le format de votre choix.</>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Format d'export */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Format d'export</Label>
            <div className="grid grid-cols-2 gap-3">
              {formatOptions.map((option) => {
                const Icon = option.icon
                return (
                  <div
                    key={option.value}
                    className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                      format === option.value
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => setFormat(option.value)}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      <Icon className="h-4 w-4" />
                      <span className="font-medium text-sm">{option.label}</span>
                    </div>
                    <p className="text-xs text-gray-600">{option.description}</p>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Options d'export */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Options d'export</Label>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="attachments"
                checked={includeAttachments}
                onCheckedChange={(checked) => setIncludeAttachments(checked as boolean)}
              />
              <Label htmlFor="attachments" className="text-sm">
                Inclure les pièces jointes
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="metadata"
                checked={includeMetadata}
                onCheckedChange={(checked) => setIncludeMetadata(checked as boolean)}
              />
              <Label htmlFor="metadata" className="text-sm">
                Inclure les métadonnées (auteur, dates, version)
              </Label>
            </div>
          </div>

          {/* Informations sur le rapport */}
          {report && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <h4 className="font-medium text-sm mb-2">Informations du rapport</h4>
              <div className="space-y-1 text-xs text-gray-600">
                <div>Titre: {report.title}</div>
                <div>Version: {report.version}</div>
                <div>Statut: {report.status}</div>
                <div>Audit: {report.audit.title}</div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Annuler
          </Button>
          <Button
            onClick={handleExport}
            disabled={loading || !report}
            className="flex items-center gap-2"
          >
            {loading ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                Export en cours...
              </>
            ) : (
              <>
                <Download className="h-4 w-4" />
                Exporter
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
